package cn.tycoding.service.impl;


import cn.tycoding.consts.OcrAiProperties;
import cn.tycoding.consts.OrcTemplateType;
import cn.tycoding.engine.OcrEngineManager;
import cn.tycoding.service.DocumentRecognitionService;
import cn.tycoding.utils.PDFContentFilter;
import com.benjaminwan.ocrlibrary.OcrResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;




import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentRecognitionServiceImpl implements DocumentRecognitionService {

	private final OcrAiProperties ocrAiProperties;


	@Override
	public String recognizeDocument(String templateType, List<?> inputs) {
		try {
			List<InputStream> inputStreams = convertInputs(inputs);
			OrcTemplateType type = OrcTemplateType.valueOf(templateType);
            return switch (type) {
				case DOCUMENT -> recognizeContract(inputStreams.get(0));
                case ID_CARD -> recognizeIdCard(inputStreams);
                case PDF -> recognizeEarningsPDF(inputStreams.get(0));
				case SCANNED_PDF -> recognizeContractWithScanned(inputStreams.get(0));
                default -> throw new IllegalArgumentException("不支持的模板类型: " + templateType);
            };
		} catch (IllegalArgumentException e) {
			throw new IllegalArgumentException("无效的模板类型: " + templateType, e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public String recognizeContract(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			String text = stripper.getText(document);
			return text;

			// 2. 清洗文本内容
			//String cleanedText = cleanExtractedText(rawText);

			// 3. 提取关键信息生成JSON
			//return extractKeyInformation(rawText);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	// 新增文本清洗方法
	private String cleanExtractedText(String rawText) {
		// 移除多余空格、换行符和特殊字符
		return rawText.replaceAll("[\\u0000-\\u001F]", "")
				.replaceAll("\\s+", " ")
				.replaceAll("(?m)^\\s+$", "")
				.trim();
	}

	// 新增关键信息提取方法
	private String extractKeyInformation(String text) {
		return text.lines()
						.filter(line -> ocrAiProperties.getKeywords().getContractFields()
								.stream().anyMatch(line::contains))
						.map(line -> String.format("\"%s\"", line.trim()))
						.collect(Collectors.joining(","));
	}


	@Override
	public String recognizeIdCard(List<InputStream> imageInputStreams) {
		try {
			// 1. 并行处理所有图片
			List<CompletableFuture<String>> futures = imageInputStreams.stream()
					.map(this::processImageAsync)
					.collect(Collectors.toList());

			// 2. 等待所有图片处理完成
			List<String> results = futures.stream()
					.map(CompletableFuture::join)
					.collect(Collectors.toList());

			// 3. 合并结果
			return String.join("\n", results);
		} catch (Exception e) {
			log.error("身份证识别失败", e);
			throw new RuntimeException("身份证识别失败", e);
		}
	}

	public CompletableFuture<String> processImageAsync(InputStream imageInputStream) {
		try {
			BufferedImage image = ImageIO.read(imageInputStream);
			return CompletableFuture.completedFuture(processImage(image));
		} catch (Exception e) {
			log.error("图片处理失败", e);
			return CompletableFuture.failedFuture(e);
		}
	}

	private String processImage(BufferedImage image) {
		try {
			if (image == null) {
				throw new IllegalArgumentException("无法读取图片数据");
			}
			// 1. 将BufferedImage保存为临时文件
			File tempFile = File.createTempFile("ocr_", ".png");
			tempFile.deleteOnExit();
			ImageIO.write(image, "PNG", tempFile);

			// 2. 使用OCR引擎管理器执行识别
			OcrResult result = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
			return result.getStrRes();
		} catch (Exception e) {
			log.error("OCR识别失败", e);
			throw new RuntimeException("OCR识别失败", e);
		}
	}

	private InputStream convertToInputStream(Object input) throws IOException {
		if (input instanceof MultipartFile) {
			return ((MultipartFile) input).getInputStream();
		} else if (input instanceof URL) {
			return ((URL) input).openStream();
		} else if (input instanceof File) {
			return new FileInputStream((File) input);
		} else if (input instanceof InputStream) {
			return (InputStream) input;
		} else {
			throw new IllegalArgumentException("不支持的输入类型: " + input.getClass().getName());
		}
	}

	private List<InputStream> convertInputs(List<?> inputs) throws IOException {
		List<InputStream> streams = new ArrayList<>();
		try {
			for (Object input : inputs) {
				streams.add(convertToInputStream(input));
			}
			return streams;
		} catch (Exception e) {
			// 确保已打开的流被关闭
			for (InputStream stream : streams) {
				stream.close();
			}
			throw e;
		}
	}


	//识别PDF
	@Override
	public String recognizeEarningsPDF(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			//关键字匹配提纯PDF
			PDDocument extracted = PDFContentFilter.extractPagesSmartToDocument(document);
			return stripper.getText(extracted);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	private String recognizeContractWithScanned(InputStream inputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(inputStream))) {
			StringBuilder result = new StringBuilder();
			PDFRenderer renderer = new PDFRenderer(document);

			// 获取配置参数
			int renderDpi = ocrAiProperties.getScannedPdf().getRenderDpi();
			int maxPages = Math.min(document.getNumberOfPages(), ocrAiProperties.getMaxPdfPages());

			log.info("开始处理扫描版PDF，共{}页，DPI:{}", maxPages, renderDpi);

			// 逐页处理
			for (int pageIndex = 0; pageIndex < maxPages; pageIndex++) {
				try {
					// 1. 将PDF页面转换为图片
					BufferedImage image = renderer.renderImageWithDPI(pageIndex, renderDpi, ImageType.RGB);

					// 2. 保存为临时文件进行OCR识别
					File tempFile = File.createTempFile("scanned_pdf_page_" + pageIndex, ".png");
					tempFile.deleteOnExit();
					ImageIO.write(image, "PNG", tempFile);

					// 3. 执行OCR识别
					OcrResult ocrResult = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
					String pageText = ocrResult.getStrRes();

					if (pageText != null && !pageText.trim().isEmpty()) {
						log.info("第{}页OCR识别完成，文本长度:{}", pageIndex + 1, pageText.length());

						// 4. 解析和格式化文本
						String formattedText = parseAndFormatContractText(pageText, pageIndex + 1);
						result.append(formattedText);

						if (pageIndex < maxPages - 1) {
							result.append("\n\n=== 第").append(pageIndex + 2).append("页 ===\n\n");
						}
					}

					// 清理临时文件
					tempFile.delete();

				} catch (Exception e) {
					log.error("处理第{}页时发生错误", pageIndex + 1, e);
					result.append("第").append(pageIndex + 1).append("页处理失败: ").append(e.getMessage()).append("\n");
				}
			}

			return result.toString();

		} catch (Exception e) {
			log.error("扫描版PDF处理失败", e);
			throw new RuntimeException("扫描版PDF处理失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解析和格式化合同文本
	 * 特别处理冒号分隔的键值对和表格数据
	 */
	private String parseAndFormatContractText(String rawText, int pageNumber) {
		StringBuilder formatted = new StringBuilder();
		formatted.append("=== 第").append(pageNumber).append("页内容 ===\n\n");

		// 按行分割文本
		String[] lines = rawText.split("\n");
		List<String> cleanLines = new ArrayList<>();

		// 清理和预处理文本行
		for (String line : lines) {
			String cleanLine = line.trim();
			if (!cleanLine.isEmpty() && cleanLine.length() > 1) {
				cleanLines.add(cleanLine);
			}
		}

		// 检测和处理甲乙双方信息
		String partyInfo = extractPartyInformation(cleanLines);
		if (!partyInfo.isEmpty()) {
			formatted.append(partyInfo).append("\n");
		}

		// 检测和处理表格信息
		String tableInfo = extractTableInformation(cleanLines);
		if (!tableInfo.isEmpty()) {
			formatted.append(tableInfo).append("\n");
		}

		// 处理其他冒号分隔的键值对
		String keyValueInfo = extractKeyValuePairs(cleanLines);
		if (!keyValueInfo.isEmpty()) {
			formatted.append(keyValueInfo).append("\n");
		}

		// 处理剩余文本
		String remainingText = extractRemainingText(cleanLines);
		if (!remainingText.isEmpty()) {
			formatted.append("其他信息：\n").append(remainingText).append("\n");
		}

		return formatted.toString();
	}
	/**
	 * 提取甲乙双方信息
	 */
	private String extractPartyInformation(List<String> lines) {
		StringBuilder partyInfo = new StringBuilder();

		// 甲方相关关键词
		String[] partyAKeywords = {"采购方", "买方", "甲方", "委托方", "发包方"};
		// 乙方相关关键词
		String[] partyBKeywords = {"供货方", "供方", "乙方", "承包方", "受托方", "卖方"};

		// 查找甲方信息
		String partyAInfo = extractPartyDetails(lines, partyAKeywords, "甲方信息");
		if (!partyAInfo.isEmpty()) {
			partyInfo.append(partyAInfo).append("\n");
		}

		// 查找乙方信息
		String partyBInfo = extractPartyDetails(lines, partyBKeywords, "乙方信息");
		if (!partyBInfo.isEmpty()) {
			partyInfo.append(partyBInfo).append("\n");
		}

		return partyInfo.toString();
	}

	/**
	 * 提取具体某一方的详细信息
	 */
	private String extractPartyDetails(List<String> lines, String[] keywords, String partyLabel) {
		StringBuilder details = new StringBuilder();
		boolean foundParty = false;
		String partyName = "";
		String address = "";
		String contact = "";

		for (int i = 0; i < lines.size(); i++) {
			String line = lines.get(i);

			// 检查是否包含关键词
			for (String keyword : keywords) {
				if (line.contains(keyword) && line.contains("：")) {
					foundParty = true;
					// 提取方名称
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						partyName = parts[1].trim();
					}
					break;
				}
			}

			// 如果找到了当事方，继续查找相关信息
			if (foundParty) {
				// 查找地址信息
				if ((line.contains("联系地址") || line.contains("地址") || line.contains("住所")) && line.contains("：")) {
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						address = parts[1].trim();
					}
				}

				// 查找联系方式
				if ((line.contains("联系人电话") || line.contains("电话") || line.contains("联系方式")) && line.contains("：")) {
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						contact = parts[1].trim();
					}
				}

				// 如果已经收集到基本信息，可以结束搜索
				if (!partyName.isEmpty() && !address.isEmpty() && !contact.isEmpty()) {
					break;
				}
			}
		}

		// 格式化输出
		if (foundParty && !partyName.isEmpty()) {
			details.append(partyLabel).append("：\n");
			details.append("名称：").append(partyName).append("\n");
			if (!address.isEmpty()) {
				details.append("联系地址：").append(address).append("\n");
			}
			if (!contact.isEmpty()) {
				details.append("联系人电话：").append(contact).append("\n");
			}
		}

		return details.toString();
	}
	/**
	 * 提取表格信息
	 */
	private String extractTableInformation(List<String> lines) {
		StringBuilder tableInfo = new StringBuilder();

		// 表格相关关键词
		String[] tableKeywords = {"序号", "品名", "商品货号", "数量", "单价", "总金额", "产品信息"};

		// 查找表格开始位置
		int tableStartIndex = -1;
		for (int i = 0; i < lines.size(); i++) {
			String line = lines.get(i);
			// 检查是否包含表格标题关键词
			for (String keyword : tableKeywords) {
				if (line.contains(keyword)) {
					tableStartIndex = i;
					break;
				}
			}
			if (tableStartIndex != -1) break;
		}

		if (tableStartIndex == -1) {
			return ""; // 没有找到表格
		}

		tableInfo.append("产品信息：\n");

		// 从表格开始位置解析数据
		List<TableRow> tableRows = new ArrayList<>();
		TableRow currentRow = null;

		for (int i = tableStartIndex; i < lines.size(); i++) {
			String line = lines.get(i).trim();

			// 跳过空行和表头
			if (line.isEmpty() || line.contains("序号") || line.contains("品名") ||
				line.contains("商品货号") || line.contains("数量") || line.contains("单价")) {
				continue;
			}

			// 检查是否是新的行开始（通常以数字开头）
			if (line.matches("^\\d+.*")) {
				// 保存上一行
				if (currentRow != null && currentRow.isValid()) {
					tableRows.add(currentRow);
				}
				// 开始新行
				currentRow = new TableRow();
				parseTableRowData(line, currentRow);
			} else if (currentRow != null) {
				// 继续解析当前行的数据
				parseTableRowData(line, currentRow);
			}

			// 检查是否到达表格结束（遇到总计等关键词）
			if (line.contains("总价") || line.contains("总金额") || line.contains("合计")) {
				if (currentRow != null && currentRow.isValid()) {
					tableRows.add(currentRow);
				}
				// 处理总计信息
				parseTotalAmount(line, tableInfo);
				break;
			}
		}

		// 添加最后一行
		if (currentRow != null && currentRow.isValid()) {
			tableRows.add(currentRow);
		}

		// 格式化输出表格数据
		for (TableRow row : tableRows) {
			tableInfo.append(row.format()).append("\n");
		}

		return tableInfo.toString();
	}

	/**
	 * 解析表格行数据
	 */
	private void parseTableRowData(String line, TableRow row) {
		// 尝试提取序号
		if (row.getSequence().isEmpty() && line.matches("^\\d+.*")) {
			String[] parts = line.split("\\s+", 2);
			if (parts.length > 0) {
				row.setSequence(parts[0]);
				if (parts.length > 1) {
					line = parts[1]; // 剩余部分继续处理
				}
			}
		}

		// 尝试提取品名（通常是数字编码）
		if (row.getProductName().isEmpty() && line.matches(".*\\d{10,}.*")) {
			String[] parts = line.split("\\s+");
			for (String part : parts) {
				if (part.matches("\\d{10,}")) {
					row.setProductName(part);
					break;
				}
			}
		}

		// 尝试提取商品货号（包含中文的描述）
		if (row.getProductCode().isEmpty() && line.matches(".*[\\u4e00-\\u9fa5].*")) {
			// 提取包含中文的部分作为商品货号
			String chinesePart = line.replaceAll("\\d+", "").trim();
			if (!chinesePart.isEmpty()) {
				row.setProductCode(chinesePart);
			}
		}

		// 尝试提取数量、单价、总金额（数字）
		String[] numbers = line.split("\\s+");
		for (String num : numbers) {
			if (num.matches("\\d+") && row.getQuantity().isEmpty()) {
				row.setQuantity(num);
			} else if (num.matches("\\d+\\.?\\d*") && row.getUnitPrice().isEmpty()) {
				row.setUnitPrice(num);
			} else if (num.matches("\\d+\\.?\\d*") && row.getTotalAmount().isEmpty()) {
				row.setTotalAmount(num);
			}
		}
	}
	/**
	 * 解析总金额信息
	 */
	private void parseTotalAmount(String line, StringBuilder tableInfo) {
		if (line.contains("总价") && line.contains("：")) {
			String[] parts = line.split("：", 2);
			if (parts.length > 1) {
				tableInfo.append("总价：").append(parts[1].trim()).append("\n");
			}
		}

		// 查找数字形式的总金额
		String[] parts = line.split("\\s+");
		for (String part : parts) {
			if (part.matches("\\d{5,}")) { // 5位以上数字可能是总金额
				tableInfo.append("总金额：").append(part).append("\n");
				break;
			}
		}
	}

	/**
	 * 提取键值对信息
	 */
	private String extractKeyValuePairs(List<String> lines) {
		StringBuilder keyValueInfo = new StringBuilder();

		for (String line : lines) {
			// 跳过已经处理过的甲乙双方信息和表格信息
			if (isPartyRelatedLine(line) || isTableRelatedLine(line)) {
				continue;
			}

			// 处理包含冒号的键值对
			if (line.contains("：") && !line.trim().startsWith("：")) {
				String[] parts = line.split("：", 2);
				if (parts.length == 2) {
					String key = parts[0].trim();
					String value = parts[1].trim();

					if (!key.isEmpty() && !value.isEmpty()) {
						keyValueInfo.append(key).append("：").append(value).append("\n");
					}
				}
			}
		}

		return keyValueInfo.toString();
	}

	/**
	 * 提取剩余文本
	 */
	private String extractRemainingText(List<String> lines) {
		StringBuilder remainingText = new StringBuilder();

		for (String line : lines) {
			// 跳过已经处理过的内容
			if (isPartyRelatedLine(line) || isTableRelatedLine(line) || line.contains("：")) {
				continue;
			}

			String cleanLine = line.trim();
			if (!cleanLine.isEmpty() && cleanLine.length() > 2) {
				remainingText.append(cleanLine).append("\n");
			}
		}

		return remainingText.toString();
	}

	/**
	 * 检查是否是甲乙双方相关的行
	 */
	private boolean isPartyRelatedLine(String line) {
		String[] partyKeywords = {"采购方", "买方", "甲方", "委托方", "发包方",
								 "供货方", "供方", "乙方", "承包方", "受托方", "卖方",
								 "联系地址", "地址", "住所", "联系人电话", "电话", "联系方式"};

		for (String keyword : partyKeywords) {
			if (line.contains(keyword)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 检查是否是表格相关的行
	 */
	private boolean isTableRelatedLine(String line) {
		String[] tableKeywords = {"序号", "品名", "商品货号", "数量", "单价", "总金额",
								 "产品信息", "总价", "合计"};

		for (String keyword : tableKeywords) {
			if (line.contains(keyword)) {
				return true;
			}
		}

		// 检查是否是表格数据行（以数字开头且包含多个数字）
		if (line.matches("^\\d+.*") && line.split("\\d+").length > 3) {
			return true;
		}

		return false;
	}
	/**
	 * 表格行数据类
	 */
	private static class TableRow {
		private String sequence = "";
		private String productName = "";
		private String productCode = "";
		private String quantity = "";
		private String unitPrice = "";
		private String totalAmount = "";

		public String getSequence() { return sequence; }
		public void setSequence(String sequence) { this.sequence = sequence; }

		public String getProductName() { return productName; }
		public void setProductName(String productName) { this.productName = productName; }

		public String getProductCode() { return productCode; }
		public void setProductCode(String productCode) { this.productCode = productCode; }

		public String getQuantity() { return quantity; }
		public void setQuantity(String quantity) { this.quantity = quantity; }

		public String getUnitPrice() { return unitPrice; }
		public void setUnitPrice(String unitPrice) { this.unitPrice = unitPrice; }

		public String getTotalAmount() { return totalAmount; }
		public void setTotalAmount(String totalAmount) { this.totalAmount = totalAmount; }

		/**
		 * 检查行数据是否有效
		 */
		public boolean isValid() {
			return !sequence.isEmpty() || !productName.isEmpty() || !productCode.isEmpty();
		}

		/**
		 * 格式化输出
		 */
		public String format() {
			StringBuilder sb = new StringBuilder();
			if (!sequence.isEmpty()) {
				sb.append("序号：").append(sequence).append("\n");
			}
			if (!productName.isEmpty()) {
				sb.append("品名：").append(productName).append("\n");
			}
			if (!productCode.isEmpty()) {
				sb.append("商品货号：").append(productCode).append("\n");
			}
			if (!quantity.isEmpty()) {
				sb.append("数量：").append(quantity).append("\n");
			}
			if (!unitPrice.isEmpty()) {
				sb.append("单价：").append(unitPrice).append("\n");
			}
			if (!totalAmount.isEmpty()) {
				sb.append("总金额：").append(totalAmount).append("\n");
			}
			return sb.toString();
		}
	}
}