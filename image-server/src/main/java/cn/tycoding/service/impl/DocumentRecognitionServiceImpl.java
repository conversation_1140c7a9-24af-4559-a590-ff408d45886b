package cn.tycoding.service.impl;


import cn.tycoding.consts.OcrAiProperties;
import cn.tycoding.consts.OrcTemplateType;
import cn.tycoding.engine.OcrEngineManager;
import cn.tycoding.service.DocumentRecognitionService;
import cn.tycoding.utils.PDFContentFilter;
import com.benjaminwan.ocrlibrary.OcrResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;




import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentRecognitionServiceImpl implements DocumentRecognitionService {

	private final OcrAiProperties ocrAiProperties;


	@Override
	public String recognizeDocument(String templateType, List<?> inputs) {
		try {
			List<InputStream> inputStreams = convertInputs(inputs);
			OrcTemplateType type = OrcTemplateType.valueOf(templateType);
            return switch (type) {
				case DOCUMENT -> recognizeContract(inputStreams.get(0));
                case ID_CARD -> recognizeIdCard(inputStreams);
                case PDF -> recognizeEarningsPDF(inputStreams.get(0));
				case SCANNED_PDF -> recognizeContractWithScanned(inputStreams.get(0));
                default -> throw new IllegalArgumentException("不支持的模板类型: " + templateType);
            };
		} catch (IllegalArgumentException e) {
			throw new IllegalArgumentException("无效的模板类型: " + templateType, e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage());
		}
	}

	@Override
	public String recognizeContract(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			String text = stripper.getText(document);
			return text;

			// 2. 清洗文本内容
			//String cleanedText = cleanExtractedText(rawText);

			// 3. 提取关键信息生成JSON
			//return extractKeyInformation(rawText);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	// 新增文本清洗方法
	private String cleanExtractedText(String rawText) {
		// 移除多余空格、换行符和特殊字符
		return rawText.replaceAll("[\\u0000-\\u001F]", "")
				.replaceAll("\\s+", " ")
				.replaceAll("(?m)^\\s+$", "")
				.trim();
	}

	// 新增关键信息提取方法
	private String extractKeyInformation(String text) {
		return text.lines()
						.filter(line -> ocrAiProperties.getKeywords().getContractFields()
								.stream().anyMatch(line::contains))
						.map(line -> String.format("\"%s\"", line.trim()))
						.collect(Collectors.joining(","));
	}


	@Override
	public String recognizeIdCard(List<InputStream> imageInputStreams) {
		try {
			// 1. 并行处理所有图片
			List<CompletableFuture<String>> futures = imageInputStreams.stream()
					.map(this::processImageAsync)
					.collect(Collectors.toList());

			// 2. 等待所有图片处理完成
			List<String> results = futures.stream()
					.map(CompletableFuture::join)
					.collect(Collectors.toList());

			// 3. 合并结果
			return String.join("\n", results);
		} catch (Exception e) {
			log.error("身份证识别失败", e);
			throw new RuntimeException("身份证识别失败", e);
		}
	}

	public CompletableFuture<String> processImageAsync(InputStream imageInputStream) {
		try {
			BufferedImage image = ImageIO.read(imageInputStream);
			return CompletableFuture.completedFuture(processImage(image));
		} catch (Exception e) {
			log.error("图片处理失败", e);
			return CompletableFuture.failedFuture(e);
		}
	}

	private String processImage(BufferedImage image) {
		try {
			if (image == null) {
				throw new IllegalArgumentException("无法读取图片数据");
			}
			// 1. 将BufferedImage保存为临时文件
			File tempFile = File.createTempFile("ocr_", ".png");
			tempFile.deleteOnExit();
			ImageIO.write(image, "PNG", tempFile);

			// 2. 使用OCR引擎管理器执行识别
			OcrResult result = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
			return result.getStrRes();
		} catch (Exception e) {
			log.error("OCR识别失败", e);
			throw new RuntimeException("OCR识别失败", e);
		}
	}

	private InputStream convertToInputStream(Object input) throws IOException {
		if (input instanceof MultipartFile) {
			return ((MultipartFile) input).getInputStream();
		} else if (input instanceof URL) {
			return ((URL) input).openStream();
		} else if (input instanceof File) {
			return new FileInputStream((File) input);
		} else if (input instanceof InputStream) {
			return (InputStream) input;
		} else {
			throw new IllegalArgumentException("不支持的输入类型: " + input.getClass().getName());
		}
	}

	private List<InputStream> convertInputs(List<?> inputs) throws IOException {
		List<InputStream> streams = new ArrayList<>();
		try {
			for (Object input : inputs) {
				streams.add(convertToInputStream(input));
			}
			return streams;
		} catch (Exception e) {
			// 确保已打开的流被关闭
			for (InputStream stream : streams) {
				stream.close();
			}
			throw e;
		}
	}


	//识别PDF
	@Override
	public String recognizeEarningsPDF(InputStream pdfInputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(pdfInputStream))) {
			// 1. 提取PDF文本内容
			PDFTextStripper stripper = new PDFTextStripper();
			// 按位置排序提取文本
			stripper.setSortByPosition(true);
			// 处理表格
			stripper.setShouldSeparateByBeads(true);
			//关键字匹配提纯PDF
			PDDocument extracted = PDFContentFilter.extractPagesSmartToDocument(document);
			return stripper.getText(extracted);
		} catch (Exception e) {
			log.error("合同处理流程异常", e);
			throw new RuntimeException("合同处理失败", e);
		}
	}

	private String recognizeContractWithScanned(InputStream inputStream) {
		try (PDDocument document = Loader.loadPDF(IOUtils.toByteArray(inputStream))) {
			StringBuilder result = new StringBuilder();
			PDFRenderer renderer = new PDFRenderer(document);

			// 获取配置参数
			int renderDpi = ocrAiProperties.getScannedPdf().getRenderDpi();
			int maxPages = Math.min(document.getNumberOfPages(), ocrAiProperties.getMaxPdfPages());

			log.info("开始处理扫描版PDF，共{}页，DPI:{}", maxPages, renderDpi);

			// 逐页处理
			for (int pageIndex = 0; pageIndex < maxPages; pageIndex++) {
				try {
					// 1. 将PDF页面转换为图片
					BufferedImage image = renderer.renderImageWithDPI(pageIndex, renderDpi, ImageType.RGB);

					// 2. 保存为临时文件进行OCR识别
					File tempFile = File.createTempFile("scanned_pdf_page_" + pageIndex, ".png");
					tempFile.deleteOnExit();
					ImageIO.write(image, "PNG", tempFile);

					// 3. 执行OCR识别
					OcrResult ocrResult = OcrEngineManager.performOcr(tempFile.getAbsolutePath());
					String pageText = ocrResult.getStrRes();

					if (pageText != null && !pageText.trim().isEmpty()) {
						log.info("第{}页OCR识别完成，文本长度:{}", pageIndex + 1, pageText.length());

						// 4. 解析和格式化文本
						String formattedText = parseAndFormatContractText(pageText, pageIndex + 1);
						result.append(formattedText);

						if (pageIndex < maxPages - 1) {
							result.append("\n\n=== 第").append(pageIndex + 2).append("页 ===\n\n");
						}
					}

					// 清理临时文件
					tempFile.delete();

				} catch (Exception e) {
					log.error("处理第{}页时发生错误", pageIndex + 1, e);
					result.append("第").append(pageIndex + 1).append("页处理失败: ").append(e.getMessage()).append("\n");
				}
			}

			return result.toString();

		} catch (Exception e) {
			log.error("扫描版PDF处理失败", e);
			throw new RuntimeException("扫描版PDF处理失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 解析和格式化合同文本
	 * 特别处理冒号分隔的键值对和表格数据
	 */
	private String parseAndFormatContractText(String rawText, int pageNumber) {
		StringBuilder formatted = new StringBuilder();
		formatted.append("=== 第").append(pageNumber).append("页内容 ===\n\n");

		// 按行分割文本
		String[] lines = rawText.split("\n");
		List<String> cleanLines = new ArrayList<>();

		// 清理和预处理文本行
		for (String line : lines) {
			String cleanLine = line.trim();
			if (!cleanLine.isEmpty() && cleanLine.length() > 1) {
				cleanLines.add(cleanLine);
			}
		}

		// 检测和处理甲乙双方信息
		String partyInfo = extractPartyInformation(cleanLines);
		if (!partyInfo.isEmpty()) {
			formatted.append(partyInfo).append("\n");
		}

		// 检测和处理表格信息
		String tableInfo = extractTableInformation(cleanLines);
		if (!tableInfo.isEmpty()) {
			formatted.append(tableInfo).append("\n");
		}

		// 检测和处理银行账户信息
		String bankInfo = extractBankInformation(cleanLines);
		if (!bankInfo.isEmpty()) {
			formatted.append(bankInfo).append("\n");
		}

		// 处理其他冒号分隔的键值对
		String keyValueInfo = extractKeyValuePairs(cleanLines);
		if (!keyValueInfo.isEmpty()) {
			formatted.append(keyValueInfo).append("\n");
		}

		// 处理剩余文本
		String remainingText = extractRemainingText(cleanLines);
		if (!remainingText.isEmpty()) {
			formatted.append("其他信息：\n").append(remainingText).append("\n");
		}

		return formatted.toString();
	}
	/**
	 * 提取甲乙双方信息
	 */
	private String extractPartyInformation(List<String> lines) {
		StringBuilder partyInfo = new StringBuilder();

		// 甲方相关关键词
		String[] partyAKeywords = {"采购方", "买方", "甲方", "委托方", "发包方"};
		// 乙方相关关键词
		String[] partyBKeywords = {"供货方", "供方", "乙方", "承包方", "受托方", "卖方"};

		// 查找甲方信息
		String partyAInfo = extractPartyDetails(lines, partyAKeywords, "甲方信息");
		if (!partyAInfo.isEmpty()) {
			partyInfo.append(partyAInfo).append("\n");
		}

		// 查找乙方信息
		String partyBInfo = extractPartyDetails(lines, partyBKeywords, "乙方信息");
		if (!partyBInfo.isEmpty()) {
			partyInfo.append(partyBInfo).append("\n");
		}

		return partyInfo.toString();
	}

	/**
	 * 提取具体某一方的详细信息 - 改进版
	 */
	private String extractPartyDetails(List<String> lines, String[] keywords, String partyLabel) {
		StringBuilder details = new StringBuilder();
		boolean foundParty = false;
		String partyName = "";
		StringBuilder addressBuilder = new StringBuilder();
		String contact = "";

		for (int i = 0; i < lines.size(); i++) {
			String line = lines.get(i).trim();

			// 检查是否包含关键词
			for (String keyword : keywords) {
				if (line.contains(keyword) && line.contains("：")) {
					foundParty = true;
					// 提取方名称
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						partyName = parts[1].trim();
						// 清理可能的多余文本
						if (partyName.contains("联系地址")) {
							String[] nameParts = partyName.split("联系地址", 2);
							partyName = nameParts[0].trim();
							if (nameParts.length > 1) {
								addressBuilder.append(nameParts[1].replace("：", "").trim());
							}
						}
					}
					break;
				}
			}

			// 如果找到了当事方，继续查找相关信息
			if (foundParty) {
				// 查找地址信息 - 改进版，支持多行地址
				if ((line.contains("联系地址") || line.contains("地址") || line.contains("住所")) && line.contains("：")) {
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						String addr = parts[1].trim();
						// 清理可能的电话号码
						if (addr.contains("联系人电话")) {
							String[] addrParts = addr.split("联系人电话", 2);
							addr = addrParts[0].trim();
							if (addrParts.length > 1) {
								contact = addrParts[1].replace("：", "").trim();
							}
						}
						if (!addr.isEmpty()) {
							if (addressBuilder.length() > 0) {
								addressBuilder.append(" ");
							}
							addressBuilder.append(addr);
						}
					}
				}

				// 查找联系方式
				if ((line.contains("联系人电话") || line.contains("电话") || line.contains("联系方式")) && line.contains("：")) {
					String[] parts = line.split("：", 2);
					if (parts.length > 1) {
						contact = parts[1].trim();
					}
				}

				// 检查是否是地址的延续行（包含省市区等地址关键词）
				if (foundParty && !line.contains("：") &&
					(line.contains("省") || line.contains("市") || line.contains("区") ||
					 line.contains("县") || line.contains("路") || line.contains("街") ||
					 line.contains("号") || line.contains("座") || line.contains("单元"))) {
					if (addressBuilder.length() > 0) {
						addressBuilder.append(" ");
					}
					addressBuilder.append(line);
				}
			}
		}

		// 格式化输出
		if (foundParty && !partyName.isEmpty()) {
			details.append(partyLabel).append("：\n");
			details.append("名称：").append(partyName).append("\n");
			String finalAddress = addressBuilder.toString().trim();
			if (!finalAddress.isEmpty()) {
				details.append("联系地址：").append(finalAddress).append("\n");
			}
			if (!contact.isEmpty()) {
				details.append("联系人电话：").append(contact).append("\n");
			}
		}

		return details.toString();
	}

	/**
	 * 提取银行账户信息
	 */
	private String extractBankInformation(List<String> lines) {
		StringBuilder bankInfo = new StringBuilder();
		String accountName = "";
		String accountNumber = "";
		String bankName = "";

		for (String line : lines) {
			String cleanLine = line.trim();

			// 提取账户名称
			if ((cleanLine.contains("账户名称") || cleanLine.contains("户名")) && cleanLine.contains("：")) {
				String[] parts = cleanLine.split("：", 2);
				if (parts.length > 1) {
					accountName = parts[1].trim();
				}
			}

			// 提取账号
			if ((cleanLine.contains("账号") || cleanLine.contains("账户")) && cleanLine.contains("：")) {
				String[] parts = cleanLine.split("：", 2);
				if (parts.length > 1) {
					accountNumber = parts[1].trim();
				}
			}

			// 提取开户行
			if ((cleanLine.contains("开户行") || cleanLine.contains("开户银行")) && cleanLine.contains("：")) {
				String[] parts = cleanLine.split("：", 2);
				if (parts.length > 1) {
					bankName = parts[1].trim();
				}
			}
		}

		// 格式化输出银行信息
		if (!accountName.isEmpty() || !accountNumber.isEmpty() || !bankName.isEmpty()) {
			bankInfo.append("银行账户信息：\n");
			if (!accountName.isEmpty()) {
				bankInfo.append("账户名称：").append(accountName).append("\n");
			}
			if (!accountNumber.isEmpty()) {
				bankInfo.append("账号：").append(accountNumber).append("\n");
			}
			if (!bankName.isEmpty()) {
				bankInfo.append("开户行：").append(bankName).append("\n");
			}
		}

		return bankInfo.toString();
	}

	/**
	 * 提取表格信息 - 改进版，专门处理商品表格
	 */
	private String extractTableInformation(List<String> lines) {
		StringBuilder tableInfo = new StringBuilder();

		// 收集所有可能的表格数据
		List<String> productCodes = new ArrayList<>();
		List<String> productNames = new ArrayList<>();
		List<String> quantities = new ArrayList<>();
		List<String> prices = new ArrayList<>();
		List<String> amounts = new ArrayList<>();

		// 扫描所有行，收集表格相关数据
		for (String line : lines) {
			String cleanLine = line.trim();

			// 跳过明显不是表格数据的行
			if (cleanLine.isEmpty() || cleanLine.length() < 3 ||
				cleanLine.contains("合同") || cleanLine.contains("条款") ||
				cleanLine.contains("法律") || cleanLine.contains("扫描") ||
				cleanLine.contains("App") || cleanLine.contains("盖章")) {
				continue;
			}

			// 识别长数字编码（产品编码）
			if (cleanLine.matches("\\d{10,}")) {
				productCodes.add(cleanLine);
				continue;
			}

			// 识别产品名称（包含中文且较长）
			if (cleanLine.matches(".*[\\u4e00-\\u9fa5].*") && cleanLine.length() > 8 &&
				!cleanLine.contains("：") && !cleanLine.contains("地址") &&
				!cleanLine.contains("电话") && !cleanLine.contains("银行")) {
				productNames.add(cleanLine);
				continue;
			}

			// 识别纯数字（可能是数量、价格、金额）
			if (cleanLine.matches("\\d+")) {
				int value = Integer.parseInt(cleanLine);
				if (value > 0 && value < 1000) {
					quantities.add(cleanLine); // 可能是数量
				} else if (value >= 1000) {
					if (value < 10000) {
						prices.add(cleanLine); // 可能是单价
					} else {
						amounts.add(cleanLine); // 可能是总金额
					}
				}
				continue;
			}

			// 识别小数（可能是单价）
			if (cleanLine.matches("\\d+\\.\\d+")) {
				prices.add(cleanLine);
				continue;
			}
		}

		// 构建表格数据
		if (!productCodes.isEmpty() || !productNames.isEmpty()) {
			tableInfo.append("产品信息：\n");

			int maxRows = Math.max(Math.max(productCodes.size(), productNames.size()),
								  Math.max(quantities.size(), Math.max(prices.size(), amounts.size())));

			for (int i = 0; i < maxRows; i++) {
				tableInfo.append("序号：").append(i + 1).append("\n");

				if (i < productCodes.size()) {
					tableInfo.append("品名：").append(productCodes.get(i)).append("\n");
				}

				if (i < productNames.size()) {
					tableInfo.append("商品货号：").append(productNames.get(i)).append("\n");
				}

				if (i < quantities.size()) {
					tableInfo.append("数量：").append(quantities.get(i)).append("\n");
				}

				if (i < prices.size()) {
					tableInfo.append("单价：").append(prices.get(i)).append("\n");
				}

				if (i < amounts.size()) {
					tableInfo.append("总金额：").append(amounts.get(i)).append("\n");
				}

				tableInfo.append("\n");
			}

			// 添加总计信息
			if (!amounts.isEmpty()) {
				try {
					double total = amounts.stream().mapToDouble(Double::parseDouble).sum();
					tableInfo.append("总金额：").append((long)total).append("\n");
				} catch (NumberFormatException e) {
					log.warn("解析总金额时出错: {}", e.getMessage());
				}
			}
		}

		return tableInfo.toString();
	}

	/**
	 * 解析表格行数据 - 改进版，更通用
	 */
	private void parseTableRowData(String line, TableRow row) {
		// 清理行数据
		String cleanLine = line.trim().replaceAll("\\s+", " ");

		// 1. 尝试提取序号（行首的数字）
		if (row.getSequence().isEmpty()) {
			String sequencePattern = "^(\\d+)[\\s\\.、].*";
			if (cleanLine.matches(sequencePattern)) {
				String sequence = cleanLine.replaceAll(sequencePattern, "$1");
				row.setSequence(sequence);
				cleanLine = cleanLine.replaceFirst("^\\d+[\\s\\.、]*", "").trim();
			}
		}

		// 2. 提取所有数字（用于后续分析）
		List<String> numbers = extractNumbers(cleanLine);

		// 3. 提取文本部分（可能是产品名称或描述）
		String textPart = cleanLine.replaceAll("[\\d\\s\\.]+", " ").trim();

		// 4. 智能分配数据
		assignDataToRow(row, numbers, textPart, cleanLine);
	}

	/**
	 * 提取行中的所有数字
	 */
	private List<String> extractNumbers(String line) {
		List<String> numbers = new ArrayList<>();
		// 匹配整数和小数
		String[] parts = line.split("\\s+");
		for (String part : parts) {
			// 匹配纯数字、小数、带逗号的数字
			if (part.matches("\\d+") || part.matches("\\d+\\.\\d+") || part.matches("\\d{1,3}(,\\d{3})*(\\.\\d+)?")) {
				numbers.add(part.replaceAll(",", "")); // 移除千位分隔符
			}
		}
		return numbers;
	}

	/**
	 * 智能分配数据到表格行
	 */
	private void assignDataToRow(TableRow row, List<String> numbers, String textPart, String originalLine) {
		// 处理产品名称/编码
		if (!textPart.isEmpty()) {
			// 如果包含中文，优先作为商品货号
			if (textPart.matches(".*[\\u4e00-\\u9fa5].*") && row.getProductCode().isEmpty()) {
				row.setProductCode(textPart);
			}
			// 如果是长数字编码，作为品名
			else if (textPart.matches(".*\\d{8,}.*") && row.getProductName().isEmpty()) {
				String productCode = extractLongNumber(textPart);
				if (!productCode.isEmpty()) {
					row.setProductName(productCode);
				}
			}
			// 其他文本作为商品货号
			else if (row.getProductCode().isEmpty()) {
				row.setProductCode(textPart);
			}
		}

		// 处理数字数据（数量、单价、总金额）
		if (!numbers.isEmpty()) {
			assignNumbers(row, numbers);
		}

		// 特殊处理：从原始行中提取长数字编码
		if (row.getProductName().isEmpty()) {
			String longNumber = extractLongNumber(originalLine);
			if (!longNumber.isEmpty()) {
				row.setProductName(longNumber);
			}
		}
	}

	/**
	 * 提取长数字编码（通常是产品编码）
	 */
	private String extractLongNumber(String text) {
		String[] parts = text.split("\\s+");
		for (String part : parts) {
			if (part.matches("\\d{8,}")) { // 8位以上数字
				return part;
			}
		}
		return "";
	}

	/**
	 * 分配数字到相应字段
	 */
	private void assignNumbers(TableRow row, List<String> numbers) {
		for (String number : numbers) {
			try {
				double value = Double.parseDouble(number);

				// 数量通常是较小的整数
				if (row.getQuantity().isEmpty() && number.matches("\\d+") && value > 0 && value < 10000) {
					row.setQuantity(number);
				}
				// 单价通常是小数或中等数值
				else if (row.getUnitPrice().isEmpty() && value > 0 && value < 100000) {
					row.setUnitPrice(number);
				}
				// 总金额通常是较大的数值
				else if (row.getTotalAmount().isEmpty() && value >= 100) {
					row.setTotalAmount(number);
				}
			} catch (NumberFormatException e) {
				log.debug("跳过无效数字: {}", number);
			}
		}

		// 如果还有未分配的数字，按顺序分配
		int index = 0;
		for (String number : numbers) {
			if (index == 0 && row.getQuantity().isEmpty()) {
				row.setQuantity(number);
			} else if (index == 1 && row.getUnitPrice().isEmpty()) {
				row.setUnitPrice(number);
			} else if (index == 2 && row.getTotalAmount().isEmpty()) {
				row.setTotalAmount(number);
			}
			index++;
		}
	}
	/**
	 * 解析总金额信息
	 */
	private void parseTotalAmount(String line, StringBuilder tableInfo) {
		if (line.contains("总价") && line.contains("：")) {
			String[] parts = line.split("：", 2);
			if (parts.length > 1) {
				tableInfo.append("总价：").append(parts[1].trim()).append("\n");
			}
		}

		// 查找数字形式的总金额
		String[] parts = line.split("\\s+");
		for (String part : parts) {
			if (part.matches("\\d{5,}")) { // 5位以上数字可能是总金额
				tableInfo.append("总金额：").append(part).append("\n");
				break;
			}
		}
	}

	/**
	 * 提取键值对信息 - 改进版，排除已处理的信息
	 */
	private String extractKeyValuePairs(List<String> lines) {
		StringBuilder keyValueInfo = new StringBuilder();

		for (String line : lines) {
			// 跳过已经处理过的甲乙双方信息、表格信息和银行信息
			if (isPartyRelatedLine(line) || isTableRelatedLine(line)) {
				continue;
			}

			// 处理包含冒号的键值对
			if (line.contains("：") && !line.trim().startsWith("：")) {
				String[] parts = line.split("：", 2);
				if (parts.length == 2) {
					String key = parts[0].trim();
					String value = parts[1].trim();

					// 排除空值和已经处理过的信息
					if (!key.isEmpty() && !value.isEmpty() &&
						!key.contains("甲方") && !key.contains("乙方") &&
						!key.contains("采购方") && !key.contains("供货方") &&
						!key.contains("账户") && !key.contains("开户行")) {
						keyValueInfo.append(key).append("：").append(value).append("\n");
					}
				}
			}
		}

		return keyValueInfo.toString();
	}

	/**
	 * 提取剩余文本 - 改进版，排除表格数据
	 */
	private String extractRemainingText(List<String> lines) {
		StringBuilder remainingText = new StringBuilder();

		for (String line : lines) {
			String cleanLine = line.trim();

			// 跳过已经处理过的内容
			if (isPartyRelatedLine(line) || isTableRelatedLine(line) || line.contains("：")) {
				continue;
			}

			// 跳过明显的表格数据
			if (cleanLine.matches("\\d+") || cleanLine.matches("\\d+\\.\\d+") ||
				cleanLine.matches("\\d{10,}")) {
				continue;
			}

			// 跳过产品名称行（包含中文且较长）
			if (cleanLine.matches(".*[\\u4e00-\\u9fa5].*") && cleanLine.length() > 8 &&
				!cleanLine.contains("合同") && !cleanLine.contains("条款") &&
				!cleanLine.contains("法律") && !cleanLine.contains("双方")) {
				continue;
			}

			// 跳过空行和过短的行
			if (cleanLine.isEmpty() || cleanLine.length() < 3) {
				continue;
			}

			// 跳过扫描应用相关信息
			if (cleanLine.contains("扫描") || cleanLine.contains("App") ||
				cleanLine.contains("亿人")) {
				continue;
			}

			// 保留有意义的文本
			if (cleanLine.length() > 5) {
				remainingText.append(cleanLine).append("\n");
			}
		}

		return remainingText.toString();
	}

	/**
	 * 检查是否是甲乙双方相关的行
	 */
	private boolean isPartyRelatedLine(String line) {
		String[] partyKeywords = {"采购方", "买方", "甲方", "委托方", "发包方",
								 "供货方", "供方", "乙方", "承包方", "受托方", "卖方",
								 "联系地址", "地址", "住所", "联系人电话", "电话", "联系方式",
								 "账户名称", "户名", "账号", "账户", "开户行", "开户银行"};

		for (String keyword : partyKeywords) {
			if (line.contains(keyword)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 检查是否是表格相关的行
	 */
	private boolean isTableRelatedLine(String line) {
		// 排除明显不是表格的内容
		String[] excludeKeywords = {
			"合同", "条款", "法律", "扫描", "App", "盖章", "签章", "授权", "代表",
			"双方", "一致", "同意", "履行", "违约", "责任", "通知", "书面", "告知",
			"争议", "诉讼", "仲裁", "法院", "生效", "终止", "中华人民共和国",
			"不可抗力", "保证", "付款", "交货", "验收"
		};

		for (String exclude : excludeKeywords) {
			if (line.contains(exclude)) {
				return false;
			}
		}

		// 真正的表格相关关键词（更严格）
		String[] tableKeywords = {
			"产品信息", "商品信息", "货物清单", "明细表", "清单"
		};

		for (String keyword : tableKeywords) {
			if (line.contains(keyword)) {
				return true;
			}
		}

		// 检查是否是纯数字行（可能是表格数据）
		if (line.matches("\\d+") || line.matches("\\d+\\.\\d+")) {
			return true;
		}

		// 检查是否是长数字编码（产品编码）
		if (line.matches("\\d{10,}")) {
			return true;
		}

		// 检查是否是产品名称（包含中文且不包含合同相关词汇）
		if (line.matches(".*[\\u4e00-\\u9fa5].*") && line.length() > 8 &&
			!line.contains("：") && !line.contains("地址") &&
			!line.contains("电话") && !line.contains("银行") &&
			!line.contains("合同") && !line.contains("条款")) {
			return true;
		}

		return false;
	}
	/**
	 * 表格行数据类
	 */
	private static class TableRow {
		private String sequence = "";
		private String productName = "";
		private String productCode = "";
		private String quantity = "";
		private String unitPrice = "";
		private String totalAmount = "";

		public String getSequence() { return sequence; }
		public void setSequence(String sequence) { this.sequence = sequence; }

		public String getProductName() { return productName; }
		public void setProductName(String productName) { this.productName = productName; }

		public String getProductCode() { return productCode; }
		public void setProductCode(String productCode) { this.productCode = productCode; }

		public String getQuantity() { return quantity; }
		public void setQuantity(String quantity) { this.quantity = quantity; }

		public String getUnitPrice() { return unitPrice; }
		public void setUnitPrice(String unitPrice) { this.unitPrice = unitPrice; }

		public String getTotalAmount() { return totalAmount; }
		public void setTotalAmount(String totalAmount) { this.totalAmount = totalAmount; }

		/**
		 * 检查行数据是否有效
		 */
		public boolean isValid() {
			return !sequence.isEmpty() || !productName.isEmpty() || !productCode.isEmpty();
		}

		/**
		 * 格式化输出
		 */
		public String format() {
			StringBuilder sb = new StringBuilder();
			if (!sequence.isEmpty()) {
				sb.append("序号：").append(sequence).append("\n");
			}
			if (!productName.isEmpty()) {
				sb.append("品名：").append(productName).append("\n");
			}
			if (!productCode.isEmpty()) {
				sb.append("商品货号：").append(productCode).append("\n");
			}
			if (!quantity.isEmpty()) {
				sb.append("数量：").append(quantity).append("\n");
			}
			if (!unitPrice.isEmpty()) {
				sb.append("单价：").append(unitPrice).append("\n");
			}
			if (!totalAmount.isEmpty()) {
				sb.append("总金额：").append(totalAmount).append("\n");
			}
			return sb.toString();
		}
	}

	/**
	 * 智能查找表格开始位置
	 */
	private int findTableStartIndex(List<String> lines, String[] tableKeywords) {
		int bestIndex = -1;
		int maxScore = 0;

		for (int i = 0; i < lines.size(); i++) {
			String line = lines.get(i);
			int score = calculateTableScore(line, tableKeywords);

			// 如果当前行得分更高，更新最佳位置
			if (score > maxScore) {
				maxScore = score;
				bestIndex = i;
			}

			// 如果找到明显的表格标题行（包含3个以上关键词），直接返回
			if (score >= 3) {
				return i;
			}
		}

		// 如果没有找到明显的表格，尝试查找数据密集的区域
		if (bestIndex == -1) {
			bestIndex = findDataDenseArea(lines);
		}

		return bestIndex;
	}

	/**
	 * 计算行的表格相关性得分
	 */
	private int calculateTableScore(String line, String[] tableKeywords) {
		int score = 0;
		String lowerLine = line.toLowerCase();

		for (String keyword : tableKeywords) {
			if (lowerLine.contains(keyword.toLowerCase())) {
				score++;
			}
		}

		// 额外加分项
		// 1. 包含多个数字的行
		if (line.replaceAll("[^0-9]", "").length() > 5) {
			score++;
		}

		// 2. 包含表格分隔符的行
		if (line.contains("|") || line.contains("│") || line.contains("┃")) {
			score += 2;
		}

		// 3. 包含多个空格分隔的短词的行（可能是表头）
		String[] parts = line.trim().split("\\s+");
		if (parts.length >= 3 && parts.length <= 8) {
			boolean allShort = true;
			for (String part : parts) {
				if (part.length() > 10) {
					allShort = false;
					break;
				}
			}
			if (allShort) {
				score++;
			}
		}

		return score;
	}

	/**
	 * 查找数据密集区域（可能是表格数据）
	 */
	private int findDataDenseArea(List<String> lines) {
		int bestIndex = -1;
		int maxDensity = 0;

		for (int i = 0; i < lines.size() - 2; i++) {
			int density = 0;

			// 检查连续3行的数据密度
			for (int j = i; j < Math.min(i + 3, lines.size()); j++) {
				String line = lines.get(j);

				// 计算数字密度
				int digitCount = line.replaceAll("[^0-9]", "").length();
				// 计算分隔符密度
				int separatorCount = line.split("\\s+").length - 1;

				density += digitCount + separatorCount;
			}

			if (density > maxDensity) {
				maxDensity = density;
				bestIndex = i;
			}
		}

		return maxDensity > 10 ? bestIndex : -1; // 只有密度足够高才认为是表格
	}
}