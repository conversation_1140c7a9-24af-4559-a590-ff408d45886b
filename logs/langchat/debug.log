2025-09-17 09:54:01,619 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-17 09:54:01,822 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 12816 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-17 09:54:01,823 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-17 09:54:04,803 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17 09:54:04,809 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17 09:54:04,916 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 80 ms. Found 0 Redis repository interfaces.
2025-09-17 09:54:06,825 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-17 09:54:06,844 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-17 09:54:06,847 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-17 09:54:06,847 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-17 09:54:07,049 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-17 09:54:07,050 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 4595 ms
2025-09-17 09:54:07,250 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-17 09:54:07,398 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-17 09:54:07,872 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-17 09:54:07,873 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-17 09:54:09,559 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=0873ec98f91054ab387fbac3dbf3c1bf, type=CHAT, model=qwen-vl-plus, provider=Q_WEN, name=通义千问VL-OCR-USE, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-7291776f57e7486ebad82dcb3bf47c6e, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,571 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,572 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,572 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,573 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,573 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,573 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,574 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,574 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,574 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,575 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:09,575 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 09:54:20,412 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.883373800s. Name resolution delay 0.017336900 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 09:54:20,415 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.883373800s. Name resolution delay 0.017336900 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 09:54:20,417 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-17 09:54:20,648 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-17 09:54:20,960 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-17 09:54:21,160 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-17 09:54:21,197 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-17 09:54:21,203 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-17 09:54:21,240 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-17 09:54:21,240 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-17 09:54:22,322 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysRoleMenu".
2025-09-17 09:54:22,323 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-17 09:54:22,668 [main] INFO  [c.t.langchat.mcp.core.config.McpAutoConfiguration] McpAutoConfiguration.java:41 - LangChat MCP模块已启用
2025-09-17 09:54:22,684 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: CompositeStrategy (优先级: 90)
2025-09-17 09:54:22,685 [main] INFO  [c.t.l.m.c.orchestration.strategy.CompositeStrategy] CompositeStrategy.java:48 - 复合编排策略已注册
2025-09-17 09:54:22,686 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: EnhancedImageGenerationStrategy (优先级: 85)
2025-09-17 09:54:22,687 [main] INFO  [c.t.l.m.c.o.s.EnhancedImageGenerationStrategy] EnhancedImageGenerationStrategy.java:49 - 增强图片生成编排策略已注册
2025-09-17 09:54:22,688 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImageGenerationStrategy (优先级: 80)
2025-09-17 09:54:22,688 [main] INFO  [c.t.l.m.c.o.strategy.ImageGenerationStrategy] ImageGenerationStrategy.java:48 - 图片生成编排策略已注册
2025-09-17 09:54:22,690 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: WebSearchStrategy (优先级: 70)
2025-09-17 09:54:22,690 [main] INFO  [c.t.l.m.c.orchestration.strategy.WebSearchStrategy] WebSearchStrategy.java:47 - 网络搜索编排策略已注册
2025-09-17 09:54:24,407 [main] WARN  [o.s.b.a.g.template.GroovyTemplateAutoConfiguration] GroovyTemplateAutoConfiguration.java:84 - Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)
2025-09-17 09:54:25,055 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8100"]
2025-09-17 09:54:25,070 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:241 - Tomcat started on port 8100 (http) with context path ''
2025-09-17 09:54:25,083 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:56 - Started LangChatApp in 24.718 seconds (process running for 27.072)
2025-09-17 09:54:25,088 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:47 - 开始注册MCP编排策略...
2025-09-17 09:54:25,089 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImagePublishStrategy (优先级: 80)
2025-09-17 09:54:25,089 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ContentCreationStrategy (优先级: 90)
2025-09-17 09:54:25,089 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: AiDrivenStrategy (优先级: 10)
2025-09-17 09:54:25,089 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:54 - MCP编排策略注册完成
2025-09-17 09:54:25,090 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:47 - 开始自动同步数据库中的MCP服务配置...
2025-09-17 09:54:25,118 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: ai-prompt-optimizer
2025-09-17 09:54:25,143 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: ai-prompt-optimizer
2025-09-17 09:54:25,221 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-api
2025-09-17 09:54:25,228 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-api
2025-09-17 09:54:25,230 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-file
2025-09-17 09:54:25,235 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-file
2025-09-17 09:54:25,238 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-email
2025-09-17 09:54:25,243 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-email
2025-09-17 09:54:25,248 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: wanx-image-generation
2025-09-17 09:54:25,253 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: wanx-image-generation
2025-09-17 09:54:25,255 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-search
2025-09-17 09:54:25,259 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-search
2025-09-17 09:54:25,259 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:52 - MCP服务配置自动同步完成
2025-09-17 09:54:25,260 [main] INFO  [c.t.l.common.core.component.CustomBannerPrinter] CustomBannerPrinter.java:34 - AIGC智能AI平台 启动完成...... 当前环境：dev
2025-09-17 10:02:21,751 [http-nio-8100-exec-1] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17 10:02:21,752 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:532 - Initializing Servlet 'dispatcherServlet'
2025-09-17 10:02:21,754 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:554 - Completed initialization in 1 ms
2025-09-17 10:19:41,209 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-17 10:19:41,324 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-17 10:19:41,328 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-17 10:19:43,252 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:29 - 推理引擎初始化完成，当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:19:43,253 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:30 - 初始化时模型配置为：Model.ONNX_PPOCR_V4(modelsDir=/models, modelType=onnx, detName=ch_PP-OCRv4_det_infer.onnx, clsName=ch_ppocr_mobile_v2.0_cls_infer.onnx, recName=ch_PP-OCRv4_rec_infer.onnx, keysName=ppocr_keys_v1.txt)， 硬件配置为：HardwareConfig(numThread=12, gpuIndex=-1)
2025-09-17 10:19:43,253 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_16366206768880102349.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:19:53,078 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9679.378000080585ms
2025-09-17 10:19:53,080 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-17 10:19:54,264 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:19:54,264 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_1817278919796783536.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:20:05,892 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时11466.252499997616ms
2025-09-17 10:20:05,895 [http-nio-8100-exec-10] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-17 10:20:06,709 [http-nio-8100-exec-10] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:20:06,710 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_5334507449440995462.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:20:13,077 [http-nio-8100-exec-10] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6245.600899994373ms
2025-09-17 10:20:13,079 [http-nio-8100-exec-10] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-17 10:26:46,099 [http-nio-8100-exec-3] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-17 10:26:46,102 [http-nio-8100-exec-3] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-17 10:26:46,102 [http-nio-8100-exec-3] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-17 10:26:46,998 [http-nio-8100-exec-3] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:26:46,998 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_68403893898257784.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:26:55,685 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时8555.247500002384ms
2025-09-17 10:26:55,687 [http-nio-8100-exec-3] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-17 10:26:56,690 [http-nio-8100-exec-3] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:26:56,690 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_1086671749217048013.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:27:06,892 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时10057.544699966908ms
2025-09-17 10:27:06,895 [http-nio-8100-exec-3] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-17 10:27:07,667 [http-nio-8100-exec-3] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:27:07,667 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_9957343709040039544.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:27:13,632 [http-nio-8100-exec-3] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时5847.28409999609ms
2025-09-17 10:27:13,634 [http-nio-8100-exec-3] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-17 10:30:24,845 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-17 10:30:24,847 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-17 10:30:24,847 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-17 10:30:25,766 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:30:25,766 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_16122463063199777429.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:30:34,709 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时8809.30009996891ms
2025-09-17 10:30:34,712 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-17 10:30:35,716 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:30:35,717 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_2211660722591518341.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:30:46,380 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时10521.174799978733ms
2025-09-17 10:30:46,383 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-17 10:30:47,143 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:30:47,143 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_12047149386541721655.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:30:53,461 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时6191.045600056648ms
2025-09-17 10:30:53,464 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-17 10:41:07,960 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:212 - 开始处理扫描版合同PDF，总页数: 3
2025-09-17 10:41:07,962 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:228 - PDF文本内容不足，开始OCR表格识别
2025-09-17 10:41:07,963 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 1 页，共 3 页
2025-09-17 10:41:08,849 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:41:08,850 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_1_13989704190892402558.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:41:17,664 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时8678.888199985027ms
2025-09-17 10:41:17,666 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 2 页，共 3 页
2025-09-17 10:41:18,667 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:41:18,667 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_2_10348789436599233988.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:41:28,975 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时10167.138199985027ms
2025-09-17 10:41:28,977 [http-nio-8100-exec-8] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:249 - 正在OCR处理第 3 页，共 3 页
2025-09-17 10:41:29,746 [http-nio-8100-exec-8] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 10:41:29,747 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\contract_page_3_15491612778285413778.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 10:41:35,696 [http-nio-8100-exec-8] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时5835.375700056553ms
2025-09-17 10:41:35,698 [http-nio-8100-exec-8] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页 ===
【文本内容】
销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App



=== 第2页 ===
【文本内容】
通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App



=== 第3页 ===
【文本内容】
（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App




2025-09-17 11:10:41,345 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 local 成功
2025-09-17 11:10:41,345 [SpringApplicationShutdownHook] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 aliyun-oss 成功
2025-09-17 11:10:41,349 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2204 - {dataSource-1} closing ...
2025-09-17 11:10:41,353 [SpringApplicationShutdownHook] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2277 - {dataSource-1} closed
2025-09-17 11:49:51,215 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-17 11:49:51,417 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 35616 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-17 11:49:51,418 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-17 11:49:54,351 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17 11:49:54,356 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17 11:49:54,479 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 91 ms. Found 0 Redis repository interfaces.
2025-09-17 11:49:56,733 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-17 11:49:56,756 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-17 11:49:56,759 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-17 11:49:56,759 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-17 11:49:56,973 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-17 11:49:56,974 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 4830 ms
2025-09-17 11:49:57,216 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-17 11:49:57,405 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-17 11:49:58,257 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-17 11:49:58,258 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-17 11:50:00,322 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=0873ec98f91054ab387fbac3dbf3c1bf, type=CHAT, model=qwen-vl-plus, provider=Q_WEN, name=通义千问VL-OCR-USE, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-7291776f57e7486ebad82dcb3bf47c6e, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,334 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,335 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,335 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,336 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,336 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,336 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,337 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,337 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,337 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,338 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:00,338 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:50:11,350 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.884142800s. Name resolution delay 0.017525200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 11:50:11,353 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.884142800s. Name resolution delay 0.017525200 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 11:50:11,354 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-17 11:50:11,596 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-17 11:50:11,918 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-17 11:50:12,094 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-17 11:50:12,129 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-17 11:50:12,135 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-17 11:50:12,162 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-17 11:50:12,163 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-17 11:50:12,526 [main] WARN  [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] AbstractApplicationContext.java:632 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'aiTestController' defined in file [D:\awork\work-aigc\aigc-manage\langchat-server\target\classes\cn\tycoding\langchat\server\controller\AiTestController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'cn.tycoding.service.DocumentRecognitionService' available: expected single matching bean but found 2: contractRecognitionServiceImpl,documentRecognitionServiceImpl
2025-09-17 11:50:12,527 [main] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 local 成功
2025-09-17 11:50:12,527 [main] INFO  [o.dromara.x.file.storage.core.FileStorageService] FileStorageService.java:734 - 销毁存储平台 aliyun-oss 成功
2025-09-17 11:50:12,529 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2204 - {dataSource-1} closing ...
2025-09-17 11:50:12,536 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:2277 - {dataSource-1} closed
2025-09-17 11:50:12,540 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Stopping service [Tomcat]
2025-09-17 11:50:12,580 [main] INFO  [o.s.b.a.logging.ConditionEvaluationReportLogger] ConditionEvaluationReportLogger.java:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-17 11:50:12,633 [main] ERROR [o.s.b.diagnostics.LoggingFailureAnalysisReporter] LoggingFailureAnalysisReporter.java:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in cn.tycoding.langchat.server.controller.AiTestController required a single bean, but 2 were found:
	- contractRecognitionServiceImpl: defined in file [D:\awork\work-aigc\aigc-manage\image-server\target\classes\cn\tycoding\service\impl\ContractRecognitionServiceImpl.class]
	- documentRecognitionServiceImpl: defined in file [D:\awork\work-aigc\aigc-manage\image-server\target\classes\cn\tycoding\service\impl\DocumentRecognitionServiceImpl.class]

This may be due to missing parameter name information

Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

Ensure that your compiler is configured to use the '-parameters' flag.
You may need to update both your build tool settings as well as your IDE.
(See https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention)


2025-09-17 11:51:58,037 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 8.0.1.Final
2025-09-17 11:51:58,226 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:50 - Starting LangChatApp using Java 17.0.14 with PID 28088 (D:\awork\work-aigc\aigc-manage\langchat-server\target\classes started by Administrator in D:\awork\work-aigc)
2025-09-17 11:51:58,227 [main] INFO  [cn.tycoding.langchat.LangChatApp] SpringApplication.java:660 - The following 1 profile is active: "dev"
2025-09-17 11:52:00,575 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:292 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-17 11:52:00,580 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:139 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-17 11:52:00,687 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:208 - Finished Spring Data repository scanning in 84 ms. Found 0 Redis repository interfaces.
2025-09-17 11:52:02,593 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:109 - Tomcat initialized with port 8100 (http)
2025-09-17 11:52:02,613 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8100"]
2025-09-17 11:52:02,615 [main] INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-09-17 11:52:02,615 [main] INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-17 11:52:02,830 [main] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-09-17 11:52:02,830 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:296 - Root WebApplicationContext: initialization completed in 4034 ms
2025-09-17 11:52:03,018 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:68 - Init DruidDataSource
2025-09-17 11:52:03,155 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:1002 - {dataSource-1} inited
2025-09-17 11:52:03,629 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysUserRole".
2025-09-17 11:52:03,630 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-17 11:52:05,295 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=0873ec98f91054ab387fbac3dbf3c1bf, type=CHAT, model=qwen-vl-plus, provider=Q_WEN, name=通义千问VL-OCR-USE, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-7291776f57e7486ebad82dcb3bf47c6e, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,308 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=23b21ee73bc3dd67ddcc4287aadb1697, type=EMBEDDING, model=text-embedding-v4, provider=Q_WEN, name=text-embedding-v4, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,309 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=30c047625a22dd3cfdc8a21d13bd7d04, type=CHAT, model=glm-4-flash, provider=ZHIPU, name=glm-4-flash-250414, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,309 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=6b1cdbb07032a117d1b4f00b865e7f23, type=CHAT, model=deepseek-v3, provider=Q_WEN, name=阿里DeepSeek-V3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,310 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=765a327613f9c29b547ec9dac3d3b2b4, type=CHAT, model=qwen-max-longcontext, provider=Q_WEN, name=千问长token模型, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=https://dashscope.aliyuncs.com, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,310 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- CHAT， 模型配置：AigcModel(id=91c235219da4e2a92ae166e38d179b89, type=CHAT, model=glm-4, provider=ZHIPU, name=glm-4-plus, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,311 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：SILICON -- CHAT， 模型配置：AigcModel(id=ac05866194643c5341a73d837b2017c8, type=CHAT, model=deepseek-ai/DeepSeek-V2.5, provider=SILICON, name=deepseek, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-univnrseuupvzarrjozodhcmnurfvvyrcujdssfggfckynxd, secretKey=null, baseUrl=https://api.siliconflow.cn/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,311 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=b3ac64325c37094da12d199d1928a37b, type=EMBEDDING, model=text-embedding-v3, provider=Q_WEN, name=通用文本向量-v3, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,311 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：ZHIPU -- TEXT_IMAGE， 模型配置：AigcModel(id=d4f776f9baa847f9c5a975c9f078e049, type=TEXT_IMAGE, model=cogview-3, provider=ZHIPU, name=图片生成, responseLimit=null, temperature=0.2, topP=0.0, apiKey=a4867e65138e4f259c5c692aef943164.6xPiD4YHC2krGJAV, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,312 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=d61f28c3ced11a1abac3215d2154c882, type=EMBEDDING, model=bge-m3:latest, provider=OLLAMA, name=ollama, responseLimit=null, temperature=0.2, topP=0.0, apiKey=null, secretKey=null, baseUrl=http://localhost:11434/, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,312 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- EMBEDDING， 模型配置：AigcModel(id=dc3858965af7b992c806ed38ddd0164b, type=EMBEDDING, model=multimodal-embedding-v1, provider=Q_WEN, name=多模态向量模型, responseLimit=null, temperature=0.2, topP=0.0, apiKey=sk-92f32b2264e24dfbabf3ec3c53e6cdae, secretKey=null, baseUrl=null, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=1024, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:05,312 [main] INFO  [c.t.langchat.ai.core.provider.ModelStoreFactory] ModelStoreFactory.java:80 - 已成功注册模型：Q_WEN -- CHAT， 模型配置：AigcModel(id=f664ddbdec3fecd2afc2ed7631c17578, type=CHAT, model=Qwen-Long, provider=Q_WEN, name=Qwen-Long, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-553d1b0396064e16af616b910a526b53, secretKey=null, baseUrl=https://dashscope.aliyuncs.com/compatible-mode/v1, endpoint=null, geminiLocation=null, geminiProject=null, azureDeploymentName=null, imageSize=null, imageQuality=null, imageStyle=null, dimension=null, providerId=null, modelVersionId=null, modelProvider=null, modelVersion=null)
2025-09-17 11:52:16,087 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - DEADLINE_EXCEEDED: deadline exceeded after 9.881047500s. Name resolution delay 0.015834800 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 11:52:16,089 [main] ERROR [io.milvus.client.AbstractMilvusGrpcClient] AbstractMilvusGrpcClient.java:3378 - Failed to initialize connection. Error: DEADLINE_EXCEEDED: deadline exceeded after 9.881047500s. Name resolution delay 0.015834800 seconds. [closed=[], open=[[wait_for_ready, buffered_nanos=**********, waiting_for_connection]]]
2025-09-17 11:52:16,091 [main] ERROR [c.t.l.ai.core.provider.EmbeddingStoreFactory] EmbeddingStoreFactory.java:103 - 向量数据库初始化失败：[Milvus] --- [MILVUS]，数据库配置信息：[AigcEmbedStore(id=c92cfbaaff366b12bc87c1082149150a, name=Milvus, provider=MILVUS, host=************, port=19530, username=null, password=null, databaseName=default, tableName=ai, dimension=1024)]
2025-09-17 11:52:16,330 [main] INFO  [cn.tycoding.langchat.server.store.AppStore] AppStore.java:44 - initialize app config list...
2025-09-17 11:52:16,609 [main] INFO  [cn.tycoding.langchat.server.store.AppChannelStore] AppChannelStore.java:52 - initialize app channel config list...
2025-09-17 11:52:16,785 [main] WARN  [c.t.l.c.oss.config.FileStorageAutoConfiguration] FileStorageAutoConfiguration.java:65 - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-09-17 11:52:16,843 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:291 - 加载本地升级版存储平台：local
2025-09-17 11:52:16,849 [main] INFO  [o.d.x.file.storage.core.FileStorageServiceBuilder] FileStorageServiceBuilder.java:325 - 加载阿里云 OSS 存储平台：aliyun-oss
2025-09-17 11:52:16,876 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:57 - 注册搜索引擎策略: BOCHAAI
2025-09-17 11:52:16,877 [main] INFO  [c.t.l.a.b.searchEngine.SearchEngineStrategyManager] SearchEngineStrategyManager.java:60 - 搜索引擎策略管理器初始化完成，共注册 1 个策略
2025-09-17 11:52:18,051 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:376 - Can not find table primary key in Class: "cn.tycoding.langchat.upms.entity.SysRoleMenu".
2025-09-17 11:52:18,052 [main] WARN  [c.b.mybatisplus.core.injector.DefaultSqlInjector] DefaultSqlInjector.java:52 - class cn.tycoding.langchat.upms.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-17 11:52:18,376 [main] INFO  [c.t.langchat.mcp.core.config.McpAutoConfiguration] McpAutoConfiguration.java:41 - LangChat MCP模块已启用
2025-09-17 11:52:18,393 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: CompositeStrategy (优先级: 90)
2025-09-17 11:52:18,393 [main] INFO  [c.t.l.m.c.orchestration.strategy.CompositeStrategy] CompositeStrategy.java:48 - 复合编排策略已注册
2025-09-17 11:52:18,394 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: EnhancedImageGenerationStrategy (优先级: 85)
2025-09-17 11:52:18,395 [main] INFO  [c.t.l.m.c.o.s.EnhancedImageGenerationStrategy] EnhancedImageGenerationStrategy.java:49 - 增强图片生成编排策略已注册
2025-09-17 11:52:18,396 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImageGenerationStrategy (优先级: 80)
2025-09-17 11:52:18,397 [main] INFO  [c.t.l.m.c.o.strategy.ImageGenerationStrategy] ImageGenerationStrategy.java:48 - 图片生成编排策略已注册
2025-09-17 11:52:18,398 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: WebSearchStrategy (优先级: 70)
2025-09-17 11:52:18,398 [main] INFO  [c.t.l.m.c.orchestration.strategy.WebSearchStrategy] WebSearchStrategy.java:47 - 网络搜索编排策略已注册
2025-09-17 11:52:20,623 [main] WARN  [o.s.b.a.g.template.GroovyTemplateAutoConfiguration] GroovyTemplateAutoConfiguration.java:84 - Cannot find template location: classpath:/templates/ (please add some templates, check your Groovy configuration, or set spring.groovy.template.check-template-location=false)
2025-09-17 11:52:21,455 [main] INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8100"]
2025-09-17 11:52:21,470 [main] INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:241 - Tomcat started on port 8100 (http) with context path ''
2025-09-17 11:52:21,484 [main] INFO  [cn.tycoding.langchat.LangChatApp] StartupInfoLogger.java:56 - Started LangChatApp in 24.581 seconds (process running for 26.232)
2025-09-17 11:52:21,490 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:47 - 开始注册MCP编排策略...
2025-09-17 11:52:21,490 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ImagePublishStrategy (优先级: 80)
2025-09-17 11:52:21,490 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: ContentCreationStrategy (优先级: 90)
2025-09-17 11:52:21,490 [main] INFO  [c.t.l.m.c.o.impl.FlexibleMcpOrchestrator] FlexibleMcpOrchestrator.java:230 - 注册编排策略: AiDrivenStrategy (优先级: 10)
2025-09-17 11:52:21,490 [main] INFO  [c.t.l.mcp.core.config.McpOrchestrationConfig] McpOrchestrationConfig.java:54 - MCP编排策略注册完成
2025-09-17 11:52:21,491 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:47 - 开始自动同步数据库中的MCP服务配置...
2025-09-17 11:52:21,522 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: ai-prompt-optimizer
2025-09-17 11:52:21,544 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: ai-prompt-optimizer
2025-09-17 11:52:21,624 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-api
2025-09-17 11:52:21,631 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-api
2025-09-17 11:52:21,634 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-file
2025-09-17 11:52:21,640 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-file
2025-09-17 11:52:21,645 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-email
2025-09-17 11:52:21,657 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-email
2025-09-17 11:52:21,667 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: wanx-image-generation
2025-09-17 11:52:21,676 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: wanx-image-generation
2025-09-17 11:52:21,681 [main] INFO  [cn.tycoding.langchat.mcp.core.protocol.McpClient] McpClient.java:54 - MCP服务已注册: builtin-search
2025-09-17 11:52:21,691 [main] INFO  [c.t.l.m.c.service.impl.AigcMcpServiceServiceImpl] AigcMcpServiceServiceImpl.java:217 - MCP服务同步成功: builtin-search
2025-09-17 11:52:21,691 [main] INFO  [c.t.l.mcp.core.config.McpServiceAutoConfiguration] McpServiceAutoConfiguration.java:52 - MCP服务配置自动同步完成
2025-09-17 11:52:21,693 [main] INFO  [c.t.l.common.core.component.CustomBannerPrinter] CustomBannerPrinter.java:34 - AIGC智能AI平台 启动完成...... 当前环境：dev
2025-09-17 11:52:32,179 [http-nio-8100-exec-1] INFO  [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17 11:52:32,180 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:532 - Initializing Servlet 'dispatcherServlet'
2025-09-17 11:52:32,181 [http-nio-8100-exec-1] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:554 - Completed initialization in 1 ms
2025-09-17 11:52:33,547 [http-nio-8100-exec-1] INFO  [cn.tycoding.langchat.auth.endpoint.AuthEndpoint] AuthEndpoint.java:93 - ====> login success，token=0296b046-dfe0-4b18-abeb-f7e36b70113d
2025-09-17 11:52:51,342 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:218 - 开始处理扫描版PDF，共3页，DPI:300
2025-09-17 11:52:53,444 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:29 - 推理引擎初始化完成，当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:52:53,444 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:30 - 初始化时模型配置为：Model.ONNX_PPOCR_V4(modelsDir=/models, modelType=onnx, detName=ch_PP-OCRv4_det_infer.onnx, clsName=ch_ppocr_mobile_v2.0_cls_infer.onnx, recName=ch_PP-OCRv4_rec_infer.onnx, keysName=ppocr_keys_v1.txt)， 硬件配置为：HardwareConfig(numThread=12, gpuIndex=-1)
2025-09-17 11:52:53,445 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_014750261199950664472.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:03,322 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时9720.116900026798ms
2025-09-17 11:53:03,322 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第1页OCR识别完成，文本长度:651
2025-09-17 11:53:04,418 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:53:04,418 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_12284816537654317385.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:09,686 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:218 - 开始处理扫描版PDF，共3页，DPI:300
2025-09-17 11:53:11,546 [http-nio-8100-exec-7] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:53:11,547 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_02792972883496773006.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:27,698 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时23117.072600007057ms
2025-09-17 11:53:27,699 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第2页OCR识别完成，文本长度:1133
2025-09-17 11:53:29,002 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：销售合同供货方：山东沂江跨境电子商务有限公司采购方：山东格益跨境电子商务集团有限公司联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三路2号楼A单元12005号园A座315-16联系人电话：18754905666联系人电话：13365499955一.产品信息（产品、规格、金额）结算币种：人民币序号总金额单价数量商品货号品名1671548258516海蓝之谜轻盈型精华乳霜60ML/20Z1747930139867海蓝之谜浓缩修护精华1002214842214.84274793013166350ML/1.7FLOZ480000总价：肆拾捌万圆整二.交货及验收1.交货方式：甲方指定地点。交（提）货后的货物运输、运费及保险费由甲方负责。如因甲方原因造成需退还或销毁的，运费、保险费及销毁费用仍由甲方承担。2.交货日期：乙方在收到甲方按本合同第三条约定所支付的相应款项后发货/交货（甲方逾期付款的，乙方有权推迟交货时间）。3.货物验收、所有权及风险转移：货物交付时即视为验收完成，货物的所有权自甲方付清全款且货交甲方时转移至甲方，货物风险自货交甲方时转移至甲方。三.付款细则1.付款方式：本合同签订后3个工作日内，甲方向乙方支付本合同约定的100%货款：乙方应于甲方付款后90天内向甲方发货，如交货方式为上门自提，乙方则应于甲方付款后90天内向甲方发出取货扫描全能王3亿人都在用的扫描App，耗时17197.871800005436ms
2025-09-17 11:53:29,003 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第1页OCR识别完成，文本长度:651
2025-09-17 11:53:29,052 [http-nio-8100-exec-4] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:53:29,053 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_216270018190127509945.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:31,192 [http-nio-8100-exec-7] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:53:31,194 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_113294155672899850900.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:42,771 [http-nio-8100-exec-4] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时13553.864800035954ms
2025-09-17 11:53:42,771 [http-nio-8100-exec-4] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第3页OCR识别完成，文本长度:94
2025-09-17 11:53:42,775 [http-nio-8100-exec-4] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页内容 ===

甲方信息：
名称：山东格益跨境电子商务集团有限公司
联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三
联系人电话：18754905666

乙方信息：
名称：山东沂江跨境电子商务有限公司
联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三
联系人电话：18754905666


产品信息：

结算币种：人民币

其他信息：
销售合同
路2号楼A单元12005号
园A座315-16
167
1548
258516
海蓝之谜轻盈型精华乳霜60ML/20Z
747930139867
海蓝之谜浓缩修护精华
100
221484
2214.84
747930131663
480000
二.交货及验收
三.付款细则
扫描全能王
3亿人都在用的扫描App



=== 第2页 ===

=== 第2页内容 ===

产品信息：
序号：1
商品货号：双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人

序号：2
商品货号：本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合

序号：3
商品货号：本合同自双方签订后生效；本合同一式 份，甲方执 份，乙方执 份，每份均具有相同

商品货号：亿人都在用的扫描App


账户名称：山东沂江跨境电子商务有限公司
账号：15873201040031575
开户行：中国农业银行股份有限公司临沂沂河新区支行

其他信息：
成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅
四．保证
五．不可抗力
因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发
生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择
或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客
攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范
围以外且经不履约方合理努力后也不能防止或避免的类似事件。
六.康洁条款
并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款
和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。
七.其他
1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人
变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。
司法文书的送达。
2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合
同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。
的法律效力。
扫描全能王
3亿人都在用的扫描App



=== 第3页 ===

=== 第3页内容 ===

产品信息：
商品货号：亿人都在用的扫描App


日期：20725年1.月24日

其他信息：
（盖章）
(盖章）
授权代表签章
L有限公司司
扫描全能王
3亿人都在用的扫描App


2025-09-17 11:53:49,581 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：通知，如乙方逾期提供货物的，本合同自动解除，乙方应返还甲方已付货款。如自提，甲方应当于乙方发出提货通知后5个工作日内自行至乙方指定提货地址提货，如甲方逾期提货的，视为甲方违约，每逾期1日应按未提货货款的万分之五向乙方支付违约金，逾期超过15日的，乙方有权单方终止合同并自行处理货物，甲方应按合同约定货款总额的20%向乙方支付违约金，乙方有权在甲方已经支付的货款中直接扣除违约金及相关损失。如给乙方造成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅费等为实现债权所支出的合理费用），乙方有权进一步向甲方追偿。2.乙方（供货方）收款账户：账户名称：山东沂江跨境电子商务有限公司账号：15873201040031575开户行：中国农业银行股份有限公司临沂沂河新区支行四．保证乙方保证对产品具有完整所有权或合法经营权并已取得产品品牌方合法有效的资质与授权；其提供的产品来源正当合法，为原厂家生产制造，性能指标应符合产品所在国的出厂标准，且不存在抵押、担保、租赁及其他影响甲方正常销售的权利瑕疵。五．不可抗力因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择不履行本合同项下规定的合约义务。本条所述的不可抗力及免责情况包括：天灾、骚乱、台风或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范围以外且经不履约方合理努力后也不能防止或避免的类似事件。六.康洁条款双方保证不得以任何形式从事任何可能涉及贿赂、腐败、敲诈、职务侵占或其他违法的行为：并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。七.其他1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。担因此产生的不利后果。双方确认，本合同首部约定的联系地址和邮箱同时适用于争议解决时司法文书的送达。2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。3.本合同自双方签订后生效；本合同一式2份，甲方执1份，乙方执1份，每份均具有相同的法律效力。2NS扫描全能王3亿人都在用的扫描App，耗时17986.091000020504ms
2025-09-17 11:53:49,581 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第2页OCR识别完成，文本长度:1133
2025-09-17 11:53:50,472 [http-nio-8100-exec-7] INFO  [com.benjaminwan.ocrlibrary.OcrEngine] OcrEngine.java:38 - 当前使用的推理引擎为：onnx-v1.2.2
2025-09-17 11:53:50,472 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:57 - 图片路径：C:\Users\<USER>\AppData\Local\Temp\scanned_pdf_page_211198283701602987899.png， 参数配置：ParamConfig(padding=50, maxSideLen=0, boxScoreThresh=0.5, boxThresh=0.3, unClipRatio=1.6, doAngle=true, mostAngle=true)
2025-09-17 11:53:59,017 [http-nio-8100-exec-7] INFO  [io.github.mymonstercat.ocr.InferenceEngine] InferenceEngine.java:59 - 识别结果为：（以下无正文，为各方的签署项）（盖章）乙方(盖章）甲方：授权代表签章：授权代表签章练日期：20725年1.月24日L有限公司司2扫描全能王3亿人都在用的扫描App，耗时8417.356599986553ms
2025-09-17 11:53:59,018 [http-nio-8100-exec-7] INFO  [c.t.service.impl.DocumentRecognitionServiceImpl] DocumentRecognitionServiceImpl.java:236 - 第3页OCR识别完成，文本长度:94
2025-09-17 11:53:59,020 [http-nio-8100-exec-7] INFO  [c.t.langchat.server.controller.AiTestController] AiTestController.java:48 - OCR识别结果文本：=== 第1页内容 ===

甲方信息：
名称：山东格益跨境电子商务集团有限公司
联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三
联系人电话：18754905666

乙方信息：
名称：山东沂江跨境电子商务有限公司
联系地址：临沂市高新区罗西街道科技创业联系地址：山东省临沂市综合保税区沂河三
联系人电话：18754905666


产品信息：

结算币种：人民币

其他信息：
销售合同
路2号楼A单元12005号
园A座315-16
167
1548
258516
海蓝之谜轻盈型精华乳霜60ML/20Z
747930139867
海蓝之谜浓缩修护精华
100
221484
2214.84
747930131663
480000
二.交货及验收
三.付款细则
扫描全能王
3亿人都在用的扫描App



=== 第2页 ===

=== 第2页内容 ===

产品信息：
序号：1
商品货号：双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人

序号：2
商品货号：本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合

序号：3
商品货号：本合同自双方签订后生效；本合同一式 份，甲方执 份，乙方执 份，每份均具有相同

商品货号：亿人都在用的扫描App


账户名称：山东沂江跨境电子商务有限公司
账号：15873201040031575
开户行：中国农业银行股份有限公司临沂沂河新区支行

其他信息：
成其他损失的（包括但不限于仓储费，以及仲裁费、诉讼费、公证费、律师费、保全费、差旅
四．保证
五．不可抗力
因不可抗力造成的合同无法履行，不履约方不承担违约责任。但不履约方须及时将不可抗力发
生的相关情况向履约方书面告知。如不履约方由于不可抗力无法履行本合同，履约方有权选择
或恶劣天气、洪水、地震、疫情（如疫情交通管制、疫情封控）、政府行为、电脑病毒、黑客
攻击、计算机系统故障、网络中断、停电、通讯故障、交通堵塞、其他在不履约方合理控制范
围以外且经不履约方合理努力后也不能防止或避免的类似事件。
六.康洁条款
并遵守与反贿赂和反腐败有关的所有法律、法规、部门规章及规范性文件。若一方违反本条款
和/或违反反贿赂法的有关规定的，守约方可立即终止本合同。
七.其他
1.双方一致同意，对于向指定联系人进行电子邮件或EMS等快递邮寄构成书面告知。如联系人
变化时，应提前书面通知对方，方可使用变更后的联系人进行商业沟通及发送通知，否则应承。
司法文书的送达。
2.本合同适用中华人民共和国法律（为本合同之目的，不含中国港澳台地区）。如在履行本合
同时发生争议，双方同意向协议签订地广州市黄埔区人民法院提起诉讼。
的法律效力。
扫描全能王
3亿人都在用的扫描App



=== 第3页 ===

=== 第3页内容 ===

产品信息：
商品货号：亿人都在用的扫描App


日期：20725年1.月24日

其他信息：
（盖章）
(盖章）
授权代表签章
L有限公司司
扫描全能王
3亿人都在用的扫描App


